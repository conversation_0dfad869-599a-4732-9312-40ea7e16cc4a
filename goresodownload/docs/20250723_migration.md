📘 数据迁移任务技术方案（ca6 & ca7 文件系统）
📌 背景
我们现有两个硬盘挂载点：ca6 与 ca7，存储结构中的照片需要迁移至新目录结构。
原始数据存储信息需要根据 MongoDB 的 prop 表中按照不同board的不同规则生成。
目标存储路径需要使用golevelstore 生成l1/l2, 使用原始数据路径作为key，使用 hash := levelStore.MurmurToInt32(keyStr)计算int32 hash，并获取对应base62编码，拼接出新的文件名。
本地pic dir: L1/L2/sid_base62.jpg
迁移过程中需要记录操作状态、更新数据库字段、并根据文件存在性做不同处理策略。
支持输入参数：
  -mt "2025-07-23T15:30:00Z"
  -disk "ca6"
  -dryrun
不需要支持断点续传，在log中写入prop_id, src, ts，dst path，old path,是否成功等信息，所有信息都写log，err同时还需要报错mongo，这样中断后，可以使用ts query旧数据处理。失败的记录，不需要重试机制。
参考本package中代码，利用golevelstore，gomongo，golog，gospeedmeter等package，使用方式参考本package的代码

🎯 目标
遍历 prop 表[参考test/e2e/fixtures/listing/properties.json]中【支持使用mt过滤】记录，按 stream 倒序处理；

添加输入参数运行ca6 或 ca7；生产环境会先在ca6上运行，再在ca7上运行；

如果图片在本机执行文件移动（rename）；
如果图片不在本机，从另一个硬盘副本复制（copy）；[只适用在ca6上运行时]

单条记录处理流程：
1）function判断是否是reso
2）function获取 原始路径，目标路径
3）rename
  a. 成功
  b. 失败
    a. ca6运行时候（仅限 ca6）, 从ca7 copy fallback 处理
    b. ca7运行时候, 从ca6新路径copy fallback 处理
  不是a成功的操作结果记录至 photo_migration_ca6 与 photo_migration_ca7 表；
  {
    "prop_id": "TRB12345678",
    "mt": "2025-07-23T15:30:00Z", #原始prop中mt
    "src": "/mnt/ca6/oldpath.jpg",
    "dst": "/mnt/new_layout/...",
    "status": "rename_failed / not_found", # renamed / copied 不需要记录
    "err_msg": "error message if any", # 这个需要吗？会有rename失败吗？如果没有，就不需要记录
    "ts": "2025-07-23T15:30:00Z" #原始prop中ts
  }
4）同步更新 prop 与 rni 表中目标路径。[
  在ca6运行完成后就更新prop，rni表，添加phoLH，tnLH，phoP
  ca7运行时直接从prop中获取到目标的phoP，然后处理，ca7运行完成后，把之前的字段"pho" "phoIDs"（根据不同board），改成对应的名称"pho_old", "phoIDs_old"]   一周后确认没有问题，删除旧的"pho_old", "phoIDs_old"字段
  DB添加:
    phoLH:[int32,int32]
    tnLH: int32
    phoP: "/L1/L2"

⚙️ 核心流程概览

          +---------------------+
          |    MongoDB: prop    |
          +---------------------+
                    |
                    | stream desc + mt filter
                    v
       +------------------------------+
       | For disk in [ca6, ca7]       |
       |   For each filtered record:  |
       |     1. Skip if src includes reso
       |     2. Build original & dest path
       |     3. Try rename             |
       |        - success: log + DB    |
       |        - fail:                |
       |           • ca6 → try copy from ca7
       |           • ca7 → try copy from ca6
       |     4. Insert log table       |
       |     5. Update prop + rni      |
       +------------------------------+
🔍 数据结构与字段说明
prop 表示例：参考test/e2e/fixtures/listing/properties.json
使用  gomongo.Coll("vow", "properties")

## MongoDB 数据类型处理最佳实践

### 常见数据类型映射
MongoDB 读取的数据类型可能与预期不同，需要进行类型安全处理：

| 字段类型 | 可能的 Go 类型 | 处理方式 |
|---------|---------------|----------|
| 整数字段 | `int`, `int32`, `int64`, `float64` | 使用类型断言 + 转换 |
| 字符串字段 | `string` | 直接类型断言 |
| 数组字段 | `[]interface{}`, `[]string`, `bson.A` | 检查类型并验证长度 |
| 布尔字段 | `bool` | 直接类型断言 |

### 类型安全处理原则
1. **避免直接类型断言**: 使用 `value.(type)` 而不是 `value.(int)`
2. **提供默认值**: 类型转换失败时返回合理的默认值
3. **记录异常**: 遇到意外类型时记录警告日志
4. **统一处理**: 创建通用的类型转换函数

### 字段验证规则
- **pho 字段**: 整数类型，值 > 0 表示有图片
- **phoIDs 字段**: 数组类型，非空数组表示有图片ID
- **src 字段**: 字符串类型，必须为有效的板块名称
- **phoLH 字段**: 存在表示已处理过，跳过处理


文件操作失败应详细记录原因；
Mongo日志记录失败情况；

哈希冲突处理详细机制

由于同一个prop可能包含多张图片，需要确保每张图片的哈希值都是唯一的，避免文件名冲突。

## 冲突检测机制
- 使用 `map[int32]bool` 类型的 `usedHashes` 跟踪已使用的哈希值
- 为每个图片路径生成哈希时，检查该哈希是否已存在
- 如果存在，判定为哈希冲突，进入解决流程

## 冲突解决策略
1. **键名后缀法**: 在原始键名后添加递增数字后缀
   - 原始键名: `path/to/image.jpg`
   - 第1次冲突: `path/to/image.jpg_1`
   - 第2次冲突: `path/to/image.jpg_2`
   - 第N次冲突: `path/to/image.jpg_N`

2. **重新哈希**: 对新的键名重新计算MurmurHash
3. **唯一性验证**: 检查新哈希值是否唯一
4. **循环重试**: 如果新哈希仍有冲突，继续递增后缀

## 最大重试次数限制
- **限制**: 最大重试1000次
- **超限处理**: 抛出panic，避免使用重复哈希值导致数据损坏
- **原因**: 1000次冲突概率极低，通常表示系统异常

## 实现示例代码
```go
func ensureUniqueHash(originalHash int32, originalKey string, usedHashes map[int32]bool) int32 {
    if !usedHashes[originalHash] {
        return originalHash
    }

    counter := 1
    for {
        newKey := fmt.Sprintf("%s_%d", originalKey, counter)
        newHash := levelStore.MurmurToInt32(newKey)

        if !usedHashes[newHash] {
            golog.Info("Hash collision resolved",
                "originalKey", originalKey,
                "newKey", newKey,
                "counter", counter)
            return newHash
        }

        counter++
        if counter > 1000 {
            panic(fmt.Sprintf("Failed to resolve hash collision after %d attempts for key: %s", counter, originalKey))
        }
    }
}
```

🧪 测试建议
使用 3~5 条包含不同状态的记录进行 dry-run 测试；

模拟 rename 成功、失败、ca6 不存在、ca7 有备份等情况；

手动验证文件是否移动到新路径；

检查 photo_migration_ca6/ca7 表是否准确反映操作结果；

确保 prop 和 rni 更新无误；

测试哈希冲突处理：
- 创建具有相同哈希值的测试数据
- 验证冲突解决机制正常工作
- 确认生成的文件名唯一且正确



const OLD_CA6_PATH = "/mnt/ca6m0/mlsimgs"
const OLD_CA7_PATH = "/mnt/ca7m0/mlsimgs"
const NEW_CA6_PATH = "/mnt/ca6m0/imgs/MLS"
const NEW_CA7_PATH = "/mnt/ca7m0/imgs/MLS"
const (
	THUMBNAIL_WIDTH  = 240
	THUMBNAIL_HEIGHT = 160
)

# prop 表查询
filter := bson.M{"mt": bson.M{"$gte": mt}}   #if input mt, or filter should be empty
projection := {"mt": 1, "sid": 1, "src": 1, "ts": 1, "board": 1, phoDl:1,  "onD": 1}

# 确认是否需要做后续处理
func needToProcess(prop bson.M) bool {     # 可以修改func name，如果返回false，就不用做后续处理
  // 如果已经处理过（有phoLH），跳过处理
  if prop["phoLH"] != nil {       # prop.pho，只ca6，因为ca6处理完成后，就都有PhoLH了
		return false
	}

  src, ok := prop["src"].(string)
  if !ok {
    return false  // src字段无效
  }

  // TRB/DDF 类型：检查 pho 字段
  if src == "TRB" || src == "DDF" {
    return getIntValue(prop, "pho") > 0
  }

  // OTW/CLG 类型：检查 phoIDs 字段
  if src == "OTW" || src == "CLG" {
    return hasNonEmptyArray(prop, "phoIDs")
  }

  return false
}

// 类型安全的整数值获取函数
func getIntValue(prop bson.M, key string) int {
  value := prop[key]
  if value == nil {
    return 0
  }

  switch v := value.(type) {
  case int:
    return v
  case int32:
    return int(v)
  case int64:
    return int(v)
  case float64:
    return int(v)  // MongoDB 有时会返回 float64
  default:
    golog.Warn("Unexpected type for field", "key", key, "type", fmt.Sprintf("%T", v), "value", v)
    return 0
  }
}

// 检查数组字段是否非空
func hasNonEmptyArray(prop bson.M, key string) bool {
  value := prop[key]
  if value == nil {
    return false
  }

  switch v := value.(type) {
  case []interface{}:
    return len(v) > 0
  case []string:
    return len(v) > 0
  case bson.A:  // MongoDB BSON 数组类型
    return len(v) > 0
  default:
    golog.Warn("Unexpected type for array field", "key", key, "type", fmt.Sprintf("%T", v), "value", v)
    return false
  }
}

func buildOriginalPath(prop bson.M) string {
  //返回相对路径，做后rename 在加上OLD_CA6_PATH或者OLD_CA7_PATH
  // 根据 prop 内容构建原始路径
  # src: DDF26226669
    ddfID: DDF23214174   #prop._id
  /#{ddfID.substr(-3)}/#{ddfID.substr(3)}_#{num}.jpg"
  本地路径：/mnt/ca6m0/mlsimgs/crea/ddf/img/174/23214174_1.jpg,  num: [1..prop.pho]
  https://img.realmaster.com/cda/828.1698685677/669/26226669_1.jpg

  # src: OTW/CLG
  oreb/creb：OTW731271  有phoIDs
  本地路径：/mnt/ca6m0/mlsimgs/oreb/mls/80/61/731271_0.jpg
  orgId：prop.orgId，对应rni的表db.mls_oreb_master_records 中的 _id: '8098061'
  sid: '731271',
  phoIDs: [ '0', '1' ]
    mui_last34 = prop.orgId?.toString().slice(-4, -2) 80
    mui_last12 = prop.orgId?.toString().slice(-2) 61
    propImgId = prop._id.slice(3) 731271
    for imgId in prop.phoIDs
      imgUrl = "/#{mui_last34}/#{mui_last12}/#{propImgId}_#{imgId}.jpg"

  creb:  CLGC4148952 有phoIDs   旧字段最后要删除（rni）
  sid: 'C4148952'，_id: '1382288'，phoIDs: [ '0' ]
  creb  /mnt/ca6m0/mlsimgs/creb/mls/22/88/C4148952_0.jpg
  oreb  /mnt/ca6m0/mlsimgs/oreb/mls/22/88/C4148952_0.jpg

  # treb: TRBN4633567
  ml_num==prop.sid
  for num in [1..prop.pho]
    if num is 1
      /#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
    else
      /#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg"
  }
  本地路径：
  /mnt/ca6m0/mlsimgs/treb/mls/1/567/1234567.jpg
  /mnt/ca6m0/mlsimgs/treb/mls/2/567/1234567_2.jpg
}

func initDirKeyStore() {
  collection := gomongo.Coll("rni", "dir_stats")
  storeTRB, err := levelStore.NewDirKeyStore(TRB, collection, 5*time.Minute)
  storeDDF, err := levelStore.NewDirKeyStore(DDF, collection, 5*time.Minute)
  storeOTW, err := levelStore.NewDirKeyStore(OTW, collection, 5*time.Minute)
  storeCLG, err := levelStore.NewDirKeyStore(CLG, collection, 5*time.Minute)


func buildNewPath(prop bson.M) string {
	// 使用 golevelstore 生成 L1/L2 路径
  // step1: 初始化：
  initDirKeyStore()
  key = buildOriginalPath(prop)
  hash := levelStore.MurmurToInt32(key)

  // 【哈希冲突处理机制】
  // 需要保证同一个prop的所有图的hash不一致，如果一致，就在key后面添加累加数字
  // 详细机制说明：
  // 1. 冲突检测：使用 map[int32]bool 跟踪已使用的哈希值
  // 2. 冲突解决：在原始key后添加递增数字后缀 (key_1, key_2, key_3...)
  // 3. 重新哈希：对新key重新计算MurmurHash
  // 4. 循环重试：直到找到唯一哈希值
  // 5. 安全限制：最大重试1000次，超过则抛出panic避免数据损坏
  //
  // 实现示例：
  // usedHashes := make(map[int32]bool)
  // uniqueHash := ensureUniqueHash(hash, key, usedHashes)
  // usedHashes[uniqueHash] = true

  base62, err := levelStore.Int32ToBase62(hash)

  // Get file path for a property
  path, err := levelStore.GetFullFilePathForProp(prop["ts"].(time.Time), prop["src"], prop["sid"])
  path: /1225/abc12   // /l1/l2

  return fmt.Sprintf("%s/%s_%s.jpg", path,prop["sid"], base62)
}

func renameOrCopy(src, dst string) error {
	// 尝试 rename，失败则 copy
}

func updateDB(prop bson.M, newPath string) error {
	// 更新 prop 和 rni 表
  每一个prop有很多图，每一个图有一个对应的int32 hash，按照顺序组成一个list，每一个prop需要更新一个包含所有图的prop.phoLH:[int32,int32]，
  rni.tnLH: int32， 小图用	newImg, err := gofile.DownloadAndResizeImage(task.URL, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT) 生成，这里可能需要修改下gofile package中DownloadAndResizeImage func. thumbKey := 第一张图key + "-t" 然后计算tnLH，实际生成小图
  phoP: "/L1/L2"

  // 根据prop 查找到对应的rni记录
  根据prop.src， 获取rni对应的表
  TrebRecords =       COLLECTION('rni','mls_treb_master_records')
  query:{_id:prop._id}
  CrebRecords = COLLECTION('rni','mls_creb_master_records')
  query:{_id:prop.orgId}
  OrebRecords = COLLECTION('rni','mls_oreb_master_records')
  query:{_id:prop.orgId}
  DDFRecords =  COLLECTION('rni','mls_crea_ddf_records')
  query:{_id:prop._id}
}

func updateDirStats(prop bson.M, newPath string) error {
  store.AddDirStats("1225", "abc12", entityCount, fileCount)
}

func init(){
  	// Initialize base[config, logging]
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}
  	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
}

func main() {
	// 处理 prop 表中的记录
}



