package main

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

// Path constants for old and new directory structures
const (
	// OLD_CA6_PATH   = "/mnt/ca6m0/mlsimgs"
	OLD_CA7_PATH   = "/mnt/ca7m0/mlsimgs"
	OLD_LOCAL_PATH = "/mnt/md0/mlsimgs"
	NEW_CA6_PATH   = "/mnt/md0/imgs/MLS"
	NEW_CA7_PATH   = "/mnt/md0/imgs/MLS"
	// When running on ca7 and falling back to copy from ca6's NEW structure,
	// use the ca6 mount path below.
	NEW_CA6_REMOTE_PATH = "/mnt/ca6m0/imgs/MLS"
)

// Supported source types
const (
	SRC_TRB = "TRB"
	SRC_DDF = "DDF"
	SRC_OTW = "OTW"
	SRC_CLG = "CLG"
)

// Migration status constants
const (
	STATUS_RENAMED         = "renamed"
	STATUS_COPIED          = "copied"
	STATUS_HARD_LINKED     = "hard_linked"
	STATUS_ALREADY_EXISTS  = "already_exists"
	STATUS_COPY_FAILED     = "copy_failed"
	STATUS_HARDLINK_FAILED = "hardlink_failed"
	STATUS_NOT_FOUND       = "not_found"
	STATUS_DRYRUN          = "dryrun"
)

// Config holds command-line configuration
type Config struct {
	MT      *time.Time // Time filter parameter
	Disk    string     // ca6 or ca7 (both use local path first, ca6 has ca7 fallback)
	DryRun  bool       // Whether to run in test mode
	SleepMs int        // Sleep duration in milliseconds between processing each property
}

// MigrationResult represents the result of a migration operation
type MigrationResult struct {
	PropID    string `json:"prop_id"`
	SrcFolder string `json:"src_folder"`
	DstFolder string `json:"dst_folder"`
	Status    string `json:"status"`
	ErrMsg    string `json:"err_msg,omitempty"`
	Disk      string `json:"disk"`
}

// ImageInfo contains information about a processed image
type ImageInfo struct {
	OriginalPath    string
	NewPath         string
	RelativeOldPath string // Original relative path from property
	Hash            int32
	Base62          string
	OperationType   string // "hard_linked", "copied", "already_exists", or "dryrun"
}

// PropertyResult represents the result of processing a property
type PropertyResult struct {
	PropID       string
	Processed    bool
	ImageCount   int
	Success      int
	Failed       int
	Renamed      int // Count of files successfully renamed (deprecated, kept for compatibility)
	Copied       int // Count of files successfully copied
	HardLinked   int // Count of files successfully hard linked
	AlreadyExist int // Count of files that already existed at destination
	Errors       []error
	PhoP         string   // New phoP path generated during processing
	ImagePaths   []string // Original image paths collected
	MainOpType   string   // Primary operation type: "hard_linked", "copied", "mixed", or "already_exists"
}

// PathBuildParams contains parameters for building file paths
type PathBuildParams struct {
	Prop     bson.M
	Src      string
	Sid      string
	ImageNum int
	ImageID  interface{} // Can be string, int, or other types depending on source
}

// DBUpdateParams contains parameters for database updates
type DBUpdateParams struct {
	Prop     bson.M
	PhoLH    []int32
	TnLH     int32
	NewPaths []string
	Disk     string // ca6 or ca7 to determine update strategy
	PhoP     string // Pre-calculated phoP path
}
