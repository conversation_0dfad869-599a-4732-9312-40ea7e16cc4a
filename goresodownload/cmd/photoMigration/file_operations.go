package main

import (
	"fmt"
	_ "image/jpeg" // Support JPEG decoding
	_ "image/png"  // Support PNG decoding
	"os"
	"path/filepath"
	"strings"

	gofile "github.com/real-rm/gofile"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
)

// replacePathPrefix safely replaces path prefix using strings.HasPrefix and strings.Replace
func replacePathPrefix(originalPath, oldPrefix, newPrefix string) string {
	if strings.HasPrefix(originalPath, oldPrefix) {
		return strings.Replace(originalPath, oldPrefix, newPrefix, 1)
	}
	return originalPath
}

// fileExists checks if a file exists at the given path
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// ensureDir ensures that the directory for the given file path exists
func ensureDir(path string) error {
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}
	return nil
}

// copyFile copies a file from source to destination
func copyFile(src, dst, propID string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	// Open source file
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer func() {
		if err := srcFile.Close(); err != nil {
			golog.Error("failed to close source file", "path", src, "error", err)
		}
	}()

	// Create destination file
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer func() {
		if err := dstFile.Close(); err != nil {
			golog.Error("failed to close destination file", "path", dst, "error", err)
		}
	}()

	// Copy content
	if _, err := srcFile.WriteTo(dstFile); err != nil {
		return fmt.Errorf("failed to copy content from %s to %s: %w", src, dst, err)
	}

	golog.Info("File copied successfully", "propID", propID, "src", src, "dst", dst)
	return nil
}

// createThumbnail creates a thumbnail from the original image using predetermined hash
func createThumbnail(originalPath, thumbnailPath string, thumbnailHash int32, propID string) error {
	// Check if original file exists
	if !fileExists(originalPath) {
		golog.Error("Thumbnail creation failed: original file not found", "propID", propID, "originalPath", originalPath)
		return fmt.Errorf("original file not found: %s", originalPath)
	}

	// Read the original image file
	imageData, err := os.ReadFile(originalPath)
	if err != nil {
		golog.Error("Thumbnail creation failed: cannot read image file", "propID", propID, "originalPath", originalPath, "error", err)
		return fmt.Errorf("failed to read image file: %w", err)
	}

	// Resize image using gofile's ResizeImageFromData
	// Using same dimensions as goresodownload.THUMBNAIL_WIDTH/HEIGHT (240x160)
	resizedImg, err := gofile.ResizeImageFromData(imageData, 240, 160)
	if err != nil {
		golog.Error("Thumbnail creation failed: cannot resize image", "propID", propID, "originalPath", originalPath, "error", err)
		return fmt.Errorf("failed to resize image: %w", err)
	}

	// Ensure thumbnail directory exists
	if err := ensureDir(thumbnailPath); err != nil {
		golog.Error("Thumbnail creation failed: cannot create directory", "propID", propID, "thumbnailPath", thumbnailPath, "error", err)
		return err
	}

	// Save thumbnail as JPEG
	savedPath, err := gofile.SaveImage(resizedImg, thumbnailPath, false) // false = JPEG format
	if err != nil {
		golog.Error("Thumbnail creation failed: cannot save thumbnail", "propID", propID, "thumbnailPath", thumbnailPath, "error", err)
		return fmt.Errorf("failed to save thumbnail: %w", err)
	}

	golog.Info("Thumbnail created", "propID", propID, "original", originalPath, "thumbnail", savedPath, "hash", thumbnailHash)
	return nil
}

// FileOperation represents a single file operation to be performed
type FileOperation struct {
	RelativePath string
	NewPath      string
	Hash         int32
	SrcPath      string
	DstPath      string
	Base62       string
	PropID       string // Add property ID for better logging
}

// BatchOperationResult represents the result of a batch file operation
type BatchOperationResult struct {
	ImageInfos    []*ImageInfo
	Success       int
	Failed        int
	Errors        []error
	SuccessfulOps []FileOperation // Store successful operations for later cleanup
}

// addFallbackOperation adds a fallback operation based on disk type and operation context
// Returns the updated fallbackOps slice and whether a fallback was added
func addFallbackOperation(op FileOperation, config Config, fallbackOps []FileOperation, result *BatchOperationResult, context string) ([]FileOperation, bool) {
	switch config.Disk {
	case "ca6":
		// If ca6 disk: try source from ca7 OLD path
		ca7Op := op
		ca7Op.SrcPath = replacePathPrefix(op.SrcPath, OLD_LOCAL_PATH, OLD_CA7_PATH)
		if ca7Op.SrcPath != op.SrcPath {
			return append(fallbackOps, ca7Op), true
		} else {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("%s failed and cannot construct ca7 OLD fallback: %s", context, op.SrcPath))
			return fallbackOps, false
		}
	case "ca7":
		// When running on ca7: try fallback from ca6 NEW structure
		// Build ca6 NEW path by replacing NEW_CA7_PATH prefix in destination with NEW_CA6_REMOTE_PATH
		ca6NewOp := op
		ca6NewOp.SrcPath = replacePathPrefix(op.DstPath, NEW_CA7_PATH, NEW_CA6_REMOTE_PATH)
		if ca6NewOp.SrcPath != op.DstPath { // replacement happened
			return append(fallbackOps, ca6NewOp), true
		} else {
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("%s failed and cannot construct ca6 NEW fallback: %s", context, op.SrcPath))
			return fallbackOps, false
		}
	default:
		result.Failed++
		result.Errors = append(result.Errors, fmt.Errorf("%s failed on %s: %s", context, config.Disk, op.SrcPath))
		return fallbackOps, false
	}
}

// batchHardLink performs batch file operations using hard links without cleanup
func batchHardLink(operations []FileOperation, config Config) (*BatchOperationResult, error) {
	result := &BatchOperationResult{
		ImageInfos: make([]*ImageInfo, 0, len(operations)),
		Success:    0,
		Failed:     0,
		Errors:     []error{},
	}

	if config.DryRun {
		// In dry run mode, just create ImageInfo objects without actual file operations
		for _, op := range operations {
			imageInfo := &ImageInfo{
				OriginalPath:    op.SrcPath,
				NewPath:         op.DstPath,
				RelativeOldPath: op.RelativePath,
				Hash:            op.Hash,
				Base62:          op.Base62,
				OperationType:   STATUS_DRYRUN,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("DRY RUN: Would create hard link", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath, "hash", op.Hash)
		}
		return result, nil
	}

	// Phase 1: Batch check file existence and collect valid operations
	validOps := make([]FileOperation, 0, len(operations))
	// Fallback operations:
	// - When disk=ca6: try source from ca7 OLD path
	// - When disk=ca7: try source from ca6 NEW path (based on computed destination path)
	ca7FallbackOps := make([]FileOperation, 0)

	for _, op := range operations {
		if fileExists(op.SrcPath) {
			validOps = append(validOps, op)
		} else {
			golog.Error("Source file not found", "propID", op.PropID, "path", op.SrcPath, "disk", config.Disk)

			// Add fallback operation based on disk type
			ca7FallbackOps, _ = addFallbackOperation(op, config, ca7FallbackOps, result, "file not found")
		}
	}

	// Phase 2: Batch create destination directories
	dirSet := make(map[string]bool)
	for _, op := range validOps {
		dir := filepath.Dir(op.DstPath)
		dirSet[dir] = true
	}
	for _, op := range ca7FallbackOps {
		dir := filepath.Dir(op.DstPath)
		dirSet[dir] = true
	}

	for dir := range dirSet {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return result, fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	// Phase 3: Create hard links for valid operations
	successfulOps := make([]FileOperation, 0, len(validOps))

	for _, op := range validOps {
		// Check if destination already exists
		if fileExists(op.DstPath) {
			golog.Warn("Destination file already exists, skipping hard link creation", "propID", op.PropID, "dst", op.DstPath)
			// Still consider this successful since the file is already in the right place
			imageInfo := &ImageInfo{
				OriginalPath:    op.SrcPath,
				NewPath:         op.DstPath,
				RelativeOldPath: op.RelativePath,
				Hash:            op.Hash,
				Base62:          op.Base62,
				OperationType:   STATUS_ALREADY_EXISTS,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			successfulOps = append(successfulOps, op)
			continue
		}

		// Create hard link
		if err := os.Link(op.SrcPath, op.DstPath); err != nil {
			golog.Error("Hard link creation failed", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath, "error", err)

			// Add fallback operation based on disk type
			var fallbackAdded bool
			ca7FallbackOps, fallbackAdded = addFallbackOperation(op, config, ca7FallbackOps, result, "hard link")
			if fallbackAdded {
				// If a fallback was added, the original operation is now validOps
				validOps = append(validOps, op)
			}
		} else {
			// Hard link creation successful
			imageInfo := &ImageInfo{
				OriginalPath:    op.SrcPath,
				NewPath:         op.DstPath,
				RelativeOldPath: op.RelativePath,
				Hash:            op.Hash,
				Base62:          op.Base62,
				OperationType:   STATUS_HARD_LINKED,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			successfulOps = append(successfulOps, op)
			golog.Info("Hard link created successfully", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath)
		}
	}

	// Phase 4: Handle fallback operations (batch copy)
	for _, op := range ca7FallbackOps {
		if !fileExists(op.SrcPath) {
			golog.Error("File not found on fallback source", "path", op.SrcPath)
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("file not found on fallback source: %s", op.SrcPath))
			continue
		}

		if err := copyFile(op.SrcPath, op.DstPath, op.PropID); err != nil {
			golog.Error("Fallback copy failed", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath, "error", err)
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("fallback copy failed: %w", err))
		} else {
			imageInfo := &ImageInfo{
				OriginalPath:    op.SrcPath,
				NewPath:         op.DstPath,
				RelativeOldPath: op.RelativePath,
				Hash:            op.Hash,
				Base62:          op.Base62,
				OperationType:   STATUS_COPIED,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("Fallback copy successful", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath)
		}
	}

	// Store successful operations for later cleanup (excluding ca7 fallback operations)
	result.SuccessfulOps = successfulOps

	return result, nil
}

// cleanup removes source files after successful operations
func cleanup(operations []FileOperation, imageInfos []*ImageInfo) error {
	for _, op := range operations {
		// Check if destination file exists before removing source
		if !fileExists(op.DstPath) {
			golog.Warn("Destination file does not exist, skipping source file deletion", "propID", op.PropID, "src", op.SrcPath, "dst", op.DstPath)
			continue
		}

		if err := os.Remove(op.SrcPath); err != nil {
			golog.Warn("Failed to remove source file after successful operation", "propID", op.PropID, "src", op.SrcPath, "error", err)
			// Don't treat this as a failure since the main operation (hard link/copy) was successful
		} else {
			golog.Info("Source file removed successfully", "propID", op.PropID, "src", op.SrcPath)
		}
	}

	return nil
}

// batchProcessImageFiles processes multiple image files with pre-calculated paths and hashes using batch operations
func batchProcessImageFiles(imagePaths []string, imageHashMap map[string]int32, imagePathMap map[string]string, src string, propID string, config Config) (*BatchOperationResult, error) {
	// Prepare batch operations
	operations := make([]FileOperation, 0, len(imagePaths))

	// Build base paths once
	var srcBasePath, dstBasePath string
	// Always use local path as default first, fallback to ca7 if not found
	srcBasePath = OLD_LOCAL_PATH
	if config.Disk == "ca6" {
		dstBasePath = NEW_CA6_PATH
	} else {
		dstBasePath = NEW_CA7_PATH
	}

	for _, relativePath := range imagePaths {
		hash := imageHashMap[relativePath]
		newPath := imagePathMap[relativePath]

		// Generate base62 for the hash
		base62, err := levelStore.Int32ToBase62(hash)
		if err != nil {
			return nil, fmt.Errorf("failed to generate base62 for %s: %w", relativePath, err)
		}

		operation := FileOperation{
			RelativePath: relativePath,
			NewPath:      newPath,
			Hash:         hash,
			SrcPath:      filepath.Join(srcBasePath, strings.TrimPrefix(relativePath, "/")),
			DstPath:      filepath.Join(dstBasePath, src, strings.TrimPrefix(newPath, "/")), // Add SRC subdirectory
			Base62:       base62,
			PropID:       propID,
		}
		operations = append(operations, operation)
	}

	// Perform batch operations
	return batchHardLink(operations, config)
}
